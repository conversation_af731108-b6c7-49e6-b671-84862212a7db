"""
Data models for power plant organizational details extraction.
"""
from typing import List, Optional, Union
from pydantic import BaseModel, Field, validator
from enum import Enum


class PowerPlantType(str, Enum):
    """Enumeration of power plant types."""
    COAL = "coal"
    GAS = "gas"
    NUCLEAR = "nuclear"
    SOLAR = "solar"
    WIND = "wind"
    HYDRO = "hydro"
    BIOMASS = "biomass"
    GEOTHERMAL = "geothermal"
    OIL = "oil"
    COMBINED_CYCLE = "combined_cycle"
    COGENERATION = "cogeneration"
    OTHER = "other"


class CurrencyCode(str, Enum):
    """ISO 4217 currency codes."""
    USD = "USD"
    EUR = "EUR"
    GBP = "GBP"
    JPY = "JPY"
    CNY = "CNY"
    INR = "INR"
    CAD = "CAD"
    AUD = "AUD"
    BRL = "BRL"
    MXN = "MXN"
    # Add more as needed


class OrganizationalDetails(BaseModel):
    """Model for power plant organizational details."""

    cfpp_type: str = Field(
        default="",
        description="Whether it's private or public, or other types of ownership"
    )

    country_name: str = Field(
        default="",
        description="Full official name of the country where the site is located"
    )

    currency_in: str = Field(
        default="",
        description="ISO 4217 currency code for financial data"
    )

    financial_year: str = Field(
        default="",
        description="Fiscal year period in MM-MM format"
    )

    organization_name: str = Field(
        default="",
        description="Official name of the company/entity that owns the plant. It is not the same as any subsidary or share holder"
    )

    plants_count: Optional[int] = Field(
        default=None,
        description="Number of power plant sites (plants) owned by the power plant. Counts distinct plant sites, not individual generating units.",
        ge=0
    )

    plant_types: List[str] = Field(
        default_factory=list,
        description="Types of power generation technologies owned by the power plant"
    )

    ppa_flag: str = Field(
        default="",
        description="Level at which the Power Purchase Agreement applies: 'Plant' (site-wide) or 'Unit' (individual generating unit)"
    )

    province: str = Field(
        default="",
        description="Sub-national region/state/province where the plant is located"
    )

    @validator('currency_in')
    def validate_currency(cls, v):
        """Validate currency code format."""
        if v and len(v) != 3:
            raise ValueError('Currency code must be 3 characters')
        return v.upper() if v else v

    @validator('plants_count')
    def validate_plants_count(cls, v):
        """Validate plants count is reasonable."""
        if v is not None and v > 1000:
            raise ValueError('Plants count seems unreasonably high')
        return v

    @validator('financial_year')
    def validate_financial_year(cls, v):
        """Validate financial year format (MM-MM)."""
        if v and v != "01-12":
            import re
            # Check if format is MM-MM (e.g., 04-03, 01-12)
            if not re.match(r'^((0[1-9]|1[0-2])-(0[1-9]|1[0-2]))$', v):
                raise ValueError('Financial year must be in MM-MM format (e.g., 04-03, 01-12)')
        return v


class SearchResult(BaseModel):
    """Model for search result from SERP API."""

    title: str
    url: str
    snippet: str
    rank: int
    source_type: str = "unknown"
    relevance_score: float = 0.0


class ScrapedContent(BaseModel):
    """Model for scraped web content."""

    url: str
    title: str
    content: str
    content_length: int
    source_type: str
    extraction_timestamp: str
    relevance_score: float = 0.0

    @validator('content_length', pre=True, always=True)
    def set_content_length(cls, v, values):
        """Automatically set content length."""
        return len(values.get('content', ''))


class ExtractionResult(BaseModel):
    """Model for LLM extraction result."""

    field_name: str
    extracted_value: Union[str, int, bool, List[str], None]
    confidence_score: float = Field(ge=0.0, le=1.0)
    source_url: str
    extraction_method: str
    source_type: str = "unknown"  # html, pdf, combined


class FieldExtractionSummary(BaseModel):
    """Summary of extraction results for a specific field."""

    field_name: str
    final_value: Union[str, int, bool, List[str], None]
    confidence_score: float = Field(ge=0.0, le=1.0)
    source_type: str  # html, pdf, combined
    source_url: str = ""
    extraction_method: str = ""
    is_complete: bool = False


class SubstationProject(BaseModel):
    """Model for projects connected to a substation."""

    description: str = Field(default="", description="Details for a single project connected to this substation")
    distance: str = Field(default="", description="The distance (e.g., in km) from the substation to that project")


class SubstationDetails(BaseModel):
    """Model for substation connection details."""

    description: str = Field(default="", description="Details for a single substation connection")
    capacity: str = Field(default="", description="The rated capacity of the connection at this substation (e.g., in MW)")
    latitude: str = Field(default="", description="The geographic latitude of the substation")
    longitude: str = Field(default="", description="The geographic longitude of the substation")
    projects: List[SubstationProject] = Field(default_factory=list, description="Projects connected to this substation")
    substation_name: str = Field(default="", description="The official name of the substation")
    substation_type: str = Field(default="", description="The type of substation (e.g., 'transmission', 'distribution', 'collector')")


class GridConnectivityMap(BaseModel):
    """Model for grid connectivity mapping."""

    description: str = Field(default="", description="Connectivity map object showing how this plant ties into the electrical grid")
    details: List[SubstationDetails] = Field(default_factory=list, description="List of substation connections")


class PPARespondent(BaseModel):
    """Model for PPA counterparty details."""

    name: str = Field(default="", description="The counterparty's name (utility, trader, corporate buyer, etc.)")
    capacity: str = Field(default="", description="The capacity volume contracted by this respondent")
    currency: str = Field(default="", description="The currency in which the price is denominated (e.g., 'USD', 'INR')")
    price: str = Field(default="", description="The contracted price per unit of energy or capacity")
    price_unit: str = Field(default="", description="The basis for the price (e.g., '$/MWh', 'INR/kW-year')")


class PPADetails(BaseModel):
    """Model for Power Purchase Agreement details."""

    description: str = Field(default="", description="Power Purchase Agreement (PPA) record governing offtake from this plant")
    capacity: str = Field(default="", description="The capacity covered by this PPA (typically in MW)")
    capacity_unit: str = Field(default="", description="The unit of that capacity (e.g., 'MW', 'kW')")
    start_date: str = Field(default="", description="The PPA's commencement date (ISO format, YYYY-MM-DD)")
    end_date: str = Field(default="", description="The PPA's termination date (ISO format, YYYY-MM-DD)")
    tenure: Optional[int] = Field(default=None, description="The numeric duration of the PPA (e.g., 20)")
    tenure_type: str = Field(default="", description="The unit for the tenure (e.g., 'Years', 'Months')")
    respondents: List[PPARespondent] = Field(default_factory=list, description="List of PPA counterparties")


class PlantDetails(BaseModel):
    """Model for comprehensive plant technical details."""

    grid_connectivity_maps: List[GridConnectivityMap] = Field(default_factory=list, description="Grid connectivity information")
    lat: str = Field(default="", description="The plant's own latitude coordinate (decimal degrees)")
    long: str = Field(default="", description="The plant's own longitude coordinate (decimal degrees)")
    name: str = Field(default="", description="The official name of the power plant")
    plant_address: str = Field(default="", description="District or city, State, Country")
    plant_id: int = Field(default=1, description="A unique identifier assigned to this plant in your system (integer)")
    plant_type: str = Field(default="", description="The technology or fuel type of the plant site")
    ppa_details: List[PPADetails] = Field(default_factory=list, description="Power Purchase Agreement details")
    units_id: List[int] = Field(default_factory=list, description="Integers from 1 to the number of units at this plant")

    @validator('lat', 'long')
    def validate_coordinates(cls, v):
        """Validate coordinate format."""
        if v and v != "":
            try:
                float(v)
            except ValueError:
                raise ValueError('Coordinates must be valid decimal numbers')
        return v




class PipelineConfig(BaseModel):
    """Configuration for the data retrieval pipeline."""

    max_search_results: int = 10
    max_scrape_pages: int = 5
    request_timeout: int = 30
    retry_attempts: int = 3
    min_content_length: int = 100
    max_content_length: int = 50000
    confidence_threshold: float = 0.7

    # API endpoints and keys will be loaded from environment
    serp_api_key: str = ""
    scraper_api_key: str = ""
    groq_api_key: str = ""

    # OpenAI configuration
    openai_api_key: str = ""
    openai_model: str = "gpt-4o-mini"
