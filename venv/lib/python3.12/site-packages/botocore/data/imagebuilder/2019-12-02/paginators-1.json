{"pagination": {"ListLifecycleExecutionResources": {"input_token": "nextToken", "output_token": "nextToken", "limit_key": "maxResults", "result_key": "resources"}, "ListLifecycleExecutions": {"input_token": "nextToken", "output_token": "nextToken", "limit_key": "maxResults", "result_key": "lifecycleExecutions"}, "ListLifecyclePolicies": {"input_token": "nextToken", "output_token": "nextToken", "limit_key": "maxResults", "result_key": "lifecyclePolicySummaryList"}, "ListWaitingWorkflowSteps": {"input_token": "nextToken", "output_token": "nextToken", "limit_key": "maxResults", "result_key": "steps"}, "ListWorkflowBuildVersions": {"input_token": "nextToken", "output_token": "nextToken", "limit_key": "maxResults", "result_key": "workflowSummaryList"}, "ListWorkflows": {"input_token": "nextToken", "output_token": "nextToken", "limit_key": "maxResults", "result_key": "workflowVersionList"}}}